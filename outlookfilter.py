import os
import re

# 判断是否为outlook邮箱
def is_outlook_email(email):
    pattern = r'@outlook\.com'
    return re.search(pattern, email) is not None

# 筛选之后的outlook邮箱
outlook_info = []

def filter_outlook_info():
# 获取当前目录下的outlook_accounts.txt
    target_file_path = os.path.join(os.getcwd(), "outlookaccount.txt")
    with open(target_file_path, "r") as file:
        for line in file:
            # 判断是否为outlook邮箱
            if is_outlook_email(line):
                outlook_info.append(line)
    # 输出到新的txt中
    with open("outlookaccount_filter.txt", "w") as file:
        for info in outlook_info:
            file.write(info)
    return outlook_info

# main
if __name__ == "__main__":
    filter_outlook_info()