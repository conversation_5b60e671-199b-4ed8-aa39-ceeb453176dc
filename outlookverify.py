import os
import outlookfilter
import imaplib

imap_server  = "outlook.office365.com"
imap_port = 993

outlook_verified_account = []

# 校验可以成功登录的邮箱
def verify_outlook_email(outlookemails):
    outlook_verified_account = [] 
    for email in outlookemails:
        username, password = email.split(":")
        # 打印分割的username和password
        print(f"username: {username}")
        print(f"password: {password}")
        server = None  
        try:
            # 校验邮箱
            print(f"正在尝试登录 {email} ...")
            server = check_email(username, password)
            print(f"{email} 登录成功")
            outlook_verified_account.append(email)
        except Exception as e:
            print(f"{email} 登录失败: {e}")
        finally:
            if server:
                try:
                    server.logout()
                except:
                    pass
    return outlook_verified_account



def check_email(email , password):
    try:
        server = imaplib.IMAP4_SSL(imap_server, imap_port)
        server.login(email, password)
        return server
    except Exception as e:
        if server:
            try:
                server.logout()
            except:
                pass
        raise
    
    
    
    