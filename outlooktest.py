import unittest
from outlookverify import check_email  # 替换 'your_module' 为实际的模块名
from receiveemail import read_latest_email

class TestVerifyOutlookEmail(unittest.TestCase):

    # def test_verify_outlook_email_success(self):
    #     # 使用有效的 Outlook 账户
    #     valid_accounts = ['<EMAIL>:!QAZ@WSX#EDC']
    #     valid_accounts.append('<EMAIL>:!QAZ@WSX#EDC')
    #     result = verify_outlook_email(valid_accounts)
    #     print(result)
    def test_verify_outlook_email_fail(self):
        server = check_email('<EMAIL>', '!QAZ@WSX#EDC')
        read_latest_email(server)
        

if __name__ == '__main__':
    unittest.main()