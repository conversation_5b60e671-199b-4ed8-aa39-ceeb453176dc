import email
import imaplib
import time
from email.header import decode_header

IMAP_SERVER = "outlook.office365.com"
IMAP_PORT = 993
EMAIL = "<EMAIL>"
PASSWORD = "!QAZ@WSX#EDC"

def decode_str(string):
    decoded, encoding = decode_header(string)[0]
    if isinstance(decoded, bytes):
        return decoded.decode(encoding or 'utf-8')
    return decoded

def get_latest_email_id(mail, folder):
    mail.select(folder)
    result, data = mail.search(None, "ALL")
    return data[0].split()[-1] if data[0] else None

def check_new_email(mail, folder, last_email_id):
    mail.select(folder)
    result, data = mail.search(None, "ALL")
    email_ids = data[0].split()
    
    if email_ids and email_ids[-1] != last_email_id:
        # 新邮件到达
        new_email_id = email_ids[-1]
        result, email_data = mail.fetch(new_email_id, "(RFC822)")
        raw_email = email_data[0][1]
        email_message = email.message_from_bytes(raw_email)

        print(f"\nNew email in {folder}:")
        print(f"From: {decode_str(email_message['From'])}")
        print(f"Subject: {decode_str(email_message['Subject'])}")
        
        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    print(part.get_payload(decode=True).decode())
                    break
        else:
            print(email_message.get_payload(decode=True).decode())

        return True  # 表示找到了新邮件

    return False  # 表示没有新邮件

def monitor_folders(mail):
    inbox_last_id = get_latest_email_id(mail, "INBOX")
    junk_last_id = get_latest_email_id(mail, "Junk")

    print("Starting to monitor for new emails...")

    while True:
        try:
            if check_new_email(mail, "INBOX", inbox_last_id) or check_new_email(mail, "Junk", junk_last_id):
                print("New email received. Stopping monitoring.")
                return  # 如果找到新邮件，立即返回

            time.sleep(10)  # 每10秒检查一次
        except Exception as e:
            print(f"Error: {str(e)}")
            time.sleep(60)  # 如果发生错误，等待一分钟后重试

def main():
    mail = None
    try:
        mail = imaplib.IMAP4_ttttttttSSL(IMAP_SERVER, IMAP_PORT)
        mail.login(EMAIL, PASSWORD)
        monitor_folders(mail)
    except Exception as e:
        print(f"An error occurred: {str(e)}")
    finally:
        if mail:
            try:
                mail.logout()
                print("Logged out successfully.")
            except:
                print("Error during logout.")

if __name__ == "__main__":
    main()