# TG机器人：https://t.me/yezioutlookbot
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
from outlookfilter import filter_outlook_info

# 设置日志
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)

TOKEN = '7497559571:AAGCiiyIHyxN-0-pKb4jnmTvPxCtfV64C9Y'

# 启动命令处理器
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    keyboard = [
        [InlineKeyboardButton("Filter mailboxes", callback_data='Filter mailboxes')]
        [InlineKeyboardButton("mailbox_listen", callback_data='mailbox_listen')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    await update.message.reply_text('请选择功能：', reply_markup=reply_markup)
# bot按钮
async def button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    await query.answer()
    if query.data == 'Filter mailboxes':
        await query.message.reply_text(text="outlook邮件筛选,启动!")
        outlook_info = filter_outlook_info()
        await query.message.reply_text(text="筛选完成，共筛选出{}个outlook邮箱".format(len(outlook_info)))
    if query.data == 'mailbox_listen':
        await query.message.reply_text(text="outlook邮件接收,启动! 正在查找可用邮箱...")
        mailbox_listen()

def main() -> None:
    application = Application.builder().token(TOKEN).build()

    application.add_handler(CommandHandler("start", start))
    application.add_handler(CallbackQueryHandler(button))

    application.run_polling()

if __name__ == '__main__':
    main()
