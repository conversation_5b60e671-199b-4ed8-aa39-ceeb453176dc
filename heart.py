import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib import colors

# 设置画布
plt.style.use('dark_background')
fig, ax = plt.subplots(figsize=(10, 8))
ax.set_xlim(-1.5, 1.5)
ax.set_ylim(-1.5, 1.5)
ax.set_aspect('equal')
ax.axis('off')  # 隐藏坐标轴获得简约效果

# 心形参数方程
def heart_x(t):
    return np.sin(t)**3

def heart_y(t):
    return (13*np.cos(t) - 5*np.cos(2*t) - 2*np.cos(3*t) - np.cos(4*t))/16

t = np.linspace(0, 2*np.pi, 200)
x_heart = heart_x(t)
y_heart = heart_y(t)

# 创建多层心形轮廓
heart_lines = []
for i in range(3):
    scale = 1.0 - i * 0.15
    line, = ax.plot([], [], color='#00ffff', lw=2-i*0.3, alpha=0.9-i*0.15)
    heart_lines.append(line)

# 创建科技感网格
grid_lines = []
n_grid = 10
for i in range(n_grid):
    angle = i * np.pi / n_grid
    line, = ax.plot([0, 1.3*np.cos(angle)], [0, 1.3*np.sin(angle)], 
                    color='#00ffff', lw=0.5, alpha=0.3)
    grid_lines.append(line)

# 创建心电图线
ecg_line, = ax.plot([], [], color='#00ffff', lw=1.5)
ecg_x = np.linspace(-1.4, 1.4, 150)

# 创建跟随心形的粒子
particles = []
for _ in range(25):
    particle, = ax.plot([], [], 'o', color='#00ffff', 
                       ms=np.random.uniform(1, 3), alpha=0.7)
    particles.append(particle)

# 创建脉冲环
pulse_circles = []
for _ in range(3):
    circle = plt.Circle((0, 0), 0, fill=False, color='#00ffff', lw=1)
    ax.add_patch(circle)
    pulse_circles.append(circle)

# 添加信息显示
bpm_text = ax.text(0, -1.3, "", fontsize=12, color='#00ffff', ha='center')

# 模拟心电图波形
def heart_waveform(t):
    # 模拟P波、QRS波群和T波的组合
    p_wave = 0.25 * np.exp(-((t % 1.0) - 0.08)**2 / 0.001)
    qrs_complex = 0.8 * np.exp(-((t % 1.0) - 0.2)**2 / 0.0004)
    t_wave = 0.35 * np.exp(-((t % 1.0) - 0.35)**2 / 0.003)
    return p_wave + qrs_complex + t_wave

# 动画更新函数
def update(frame):
    t_beat = frame / 20  # 控制心跳频率
    beat_intensity = heart_waveform(t_beat)
    
    # 更新心形轮廓
    for i, line in enumerate(heart_lines):
        scale = (1.0 - i * 0.15) * (1 + beat_intensity * 0.1)
        line.set_data(x_heart * scale, y_heart * scale)
        line.set_alpha(min(1.0, (0.9-i*0.15) + beat_intensity * 0.1))
    
    # 更新网格线 - 轻微旋转产生科技感
    for i, line in enumerate(grid_lines):
        angle = (i * np.pi / n_grid) + t_beat * 0.1
        line.set_data([0, 1.3*np.cos(angle)], [0, 1.3*np.sin(angle)])
    
    # 更新粒子
    for i, particle in enumerate(particles):
        idx = (frame * 2 + i * 10) % len(x_heart)
        rad_factor = (0.8 + 0.2 * np.random.random()) * (1 + beat_intensity * 0.1)
        px = x_heart[idx] * rad_factor + np.random.normal(0, 0.02)
        py = y_heart[idx] * rad_factor + np.random.normal(0, 0.02)
        particle.set_data([px], [py])
        particle.set_alpha(0.4 + 0.6 * beat_intensity)
    
    # 更新心电图
    ecg_wave = np.array([heart_waveform(ex * 0.2 - t_beat) for ex in ecg_x])
    ecg_line.set_data(ecg_x, ecg_wave * 0.3 - 1.0)
    
    # 更新脉冲圆环
    for i, circle in enumerate(pulse_circles):
        if frame % 20 == i * 6:  # 错开时间
            circle.set_radius(0)
        radius = ((frame - i * 6) % 20) / 10.0
        if 0 <= radius <= 1.8:
            circle.set_radius(radius)
            circle.set_alpha(max(0, 0.7 - radius/2.0))
        else:
            circle.set_alpha(0)
    
    # 更新心率显示 - 模拟真实变化
    heart_rate = 70 + int(10 * np.sin(frame/30))
    bpm_text.set_text(f"HEART RATE: {heart_rate} BPM")
    
    return heart_lines + grid_lines + [ecg_line, bpm_text] + particles + pulse_circles

# 创建动画 (50ms间隔≈20fps，接近人眼感知流畅度)
ani = FuncAnimation(fig, update, frames=200, interval=50, blit=True)

plt.tight_layout()
plt.show()